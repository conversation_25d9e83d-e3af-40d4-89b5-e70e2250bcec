#!/usr/bin/env python3
"""
Compare two PDF files to understand structural differences
"""

import sys
from pypdf import PdfReader
from pypdf.generic import DictionaryObject, ArrayObject


def analyze_pdf_structure(file_path: str):
    """Analyze PDF internal structure"""
    print(f"Analyzing PDF structure: {file_path}")
    print("=" * 60)
    
    try:
        with open(file_path, 'rb') as file:
            reader = PdfReader(file)
            
            # Basic info
            print(f"Number of pages: {len(reader.pages)}")
            
            # Analyze trailer
            trailer = reader.trailer
            print(f"Trailer keys: {list(trailer.keys())}")
            
            # Analyze root object
            if '/Root' in trailer:
                root = trailer['/Root']
                if hasattr(root, 'get_object'):
                    root_obj = root.get_object()
                else:
                    root_obj = root
                    
                print(f"Root object keys: {list(root_obj.keys())}")
                
                # Check AcroForm
                if '/AcroForm' in root_obj:
                    acroform = root_obj['/AcroForm']
                    if hasattr(acroform, 'get_object'):
                        acroform_obj = acroform.get_object()
                    else:
                        acroform_obj = acroform
                    print(f"AcroForm keys: {list(acroform_obj.keys())}")
                    
                    if '/Fields' in acroform_obj:
                        fields = acroform_obj['/Fields']
                        if hasattr(fields, 'get_object'):
                            fields_obj = fields.get_object()
                        else:
                            fields_obj = fields
                        print(f"Number of form fields: {len(fields_obj) if isinstance(fields_obj, (list, ArrayObject)) else 'Unknown'}")
                
                # Check for other important objects
                important_keys = ['/Pages', '/Names', '/Dests', '/ViewerPreferences', 
                                '/PageLabels', '/Threads', '/OpenAction', '/AA', '/URI', '/Metadata']
                
                for key in important_keys:
                    if key in root_obj:
                        print(f"{key}: Present")
                    else:
                        print(f"{key}: Missing")
            
            # Check Info dictionary
            if hasattr(reader, 'metadata') and reader.metadata:
                print(f"\nMetadata entries: {len(reader.metadata)}")
                for key, value in reader.metadata.items():
                    print(f"  {key}: {str(value)[:50]}...")
            else:
                print("No metadata found")
            
            # Analyze pages
            print(f"\nPage analysis:")
            for i, page in enumerate(reader.pages):
                page_obj = page
                print(f"Page {i+1}:")
                print(f"  Keys: {list(page_obj.keys())}")
                
                # Check for resources
                if '/Resources' in page_obj:
                    resources = page_obj['/Resources']
                    if hasattr(resources, 'get_object'):
                        resources_obj = resources.get_object()
                    else:
                        resources_obj = resources
                    print(f"  Resources: {list(resources_obj.keys()) if isinstance(resources_obj, DictionaryObject) else 'None'}")
                
                # Check for annotations
                if '/Annots' in page_obj:
                    annots = page_obj['/Annots']
                    if hasattr(annots, 'get_object'):
                        annots_obj = annots.get_object()
                    else:
                        annots_obj = annots
                    print(f"  Annotations: {len(annots_obj) if isinstance(annots_obj, (list, ArrayObject)) else 'None'}")
                else:
                    print(f"  Annotations: None")
                
                if i >= 2:  # Only show first 3 pages to avoid too much output
                    print(f"  ... (showing only first 3 pages)")
                    break
            
            # Try to estimate object count
            try:
                # This is a rough estimate
                xref_table = reader.xref
                print(f"\nCross-reference table sections: {len(xref_table)}")
                
                total_objects = 0
                for section in xref_table:
                    total_objects += len(section)
                print(f"Estimated total objects: {total_objects}")
                
            except Exception as xref_error:
                print(f"Could not analyze xref table: {xref_error}")
            
            return {
                'pages': len(reader.pages),
                'has_acroform': '/AcroForm' in root_obj if '/Root' in trailer else False,
                'has_metadata': bool(reader.metadata),
                'root_keys': list(root_obj.keys()) if '/Root' in trailer else [],
            }
            
    except Exception as e:
        print(f"Error analyzing PDF: {e}")
        import traceback
        traceback.print_exc()
        return None


def compare_pdfs(file1: str, file2: str):
    """Compare two PDF files"""
    print(f"Comparing PDFs: {file1} vs {file2}")
    print("=" * 80)
    
    # Get file sizes
    import os
    size1 = os.path.getsize(file1)
    size2 = os.path.getsize(file2)
    
    print(f"File sizes:")
    print(f"  {file1}: {size1:,} bytes")
    print(f"  {file2}: {size2:,} bytes")
    print(f"  Difference: {abs(size1 - size2):,} bytes ({abs(size1 - size2) / max(size1, size2) * 100:.1f}%)")
    print()
    
    # Analyze both files
    print("FIRST FILE:")
    print("-" * 40)
    info1 = analyze_pdf_structure(file1)
    
    print("\n\nSECOND FILE:")
    print("-" * 40)
    info2 = analyze_pdf_structure(file2)
    
    # Compare results
    if info1 and info2:
        print("\n\nCOMPARISON SUMMARY:")
        print("=" * 40)
        
        print(f"Pages: {info1['pages']} vs {info2['pages']}")
        print(f"AcroForm: {info1['has_acroform']} vs {info2['has_acroform']}")
        print(f"Metadata: {info1['has_metadata']} vs {info2['has_metadata']}")
        
        # Compare root keys
        keys1 = set(info1['root_keys'])
        keys2 = set(info2['root_keys'])
        
        missing_in_2 = keys1 - keys2
        missing_in_1 = keys2 - keys1
        
        if missing_in_2:
            print(f"Keys missing in {file2}: {missing_in_2}")
        if missing_in_1:
            print(f"Keys missing in {file1}: {missing_in_1}")
        
        if not missing_in_1 and not missing_in_2:
            print("Both files have the same root object keys")


def main():
    """Main function"""
    if len(sys.argv) < 3:
        print("Usage: python compare_pdfs.py <file1.pdf> <file2.pdf>")
        print("Default: comparing fw9.pdf and out.pdf")
        file1 = "fw9.pdf"
        file2 = "out.pdf"
    else:
        file1 = sys.argv[1]
        file2 = sys.argv[2]
    
    compare_pdfs(file1, file2)


if __name__ == "__main__":
    main()
