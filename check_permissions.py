#!/usr/bin/env python3
"""
Check file system permissions and PDF internal permissions
"""

import os
import sys
import stat
from pypdf import PdfReader
from pypdf.generic import DictionaryObject


def check_file_permissions(file_path: str):
    """Check file system permissions"""
    print(f"File System Permissions for: {file_path}")
    print("=" * 50)
    
    try:
        # Get file stats
        file_stat = os.stat(file_path)
        
        # Check basic file info
        print(f"File size: {file_stat.st_size:,} bytes")
        print(f"Owner UID: {file_stat.st_uid}")
        print(f"Group GID: {file_stat.st_gid}")
        
        # Check permissions
        mode = file_stat.st_mode
        
        # Owner permissions
        owner_read = bool(mode & stat.S_IRUSR)
        owner_write = bool(mode & stat.S_IWUSR)
        owner_exec = bool(mode & stat.S_IXUSR)
        
        # Group permissions
        group_read = bool(mode & stat.S_IRGRP)
        group_write = bool(mode & stat.S_IWGRP)
        group_exec = bool(mode & stat.S_IXGRP)
        
        # Other permissions
        other_read = bool(mode & stat.S_IROTH)
        other_write = bool(mode & stat.S_IWOTH)
        other_exec = bool(mode & stat.S_IXOTH)
        
        print(f"\nPermissions (octal): {oct(mode)[-3:]}")
        print(f"Owner:  Read={owner_read}, Write={owner_write}, Execute={owner_exec}")
        print(f"Group:  Read={group_read}, Write={group_write}, Execute={group_exec}")
        print(f"Others: Read={other_read}, Write={other_write}, Execute={other_exec}")
        
        # Check if current user can read/write
        current_uid = os.getuid()
        current_gid = os.getgid()
        
        print(f"\nCurrent user UID: {current_uid}")
        print(f"Current user GID: {current_gid}")
        
        can_read = os.access(file_path, os.R_OK)
        can_write = os.access(file_path, os.W_OK)
        
        print(f"Current user can read: {can_read}")
        print(f"Current user can write: {can_write}")
        
        return can_read, can_write
        
    except Exception as e:
        print(f"Error checking file permissions: {e}")
        return False, False


def check_pdf_security(file_path: str):
    """Check PDF internal security and permissions"""
    print(f"\nPDF Security Analysis for: {file_path}")
    print("=" * 50)
    
    try:
        with open(file_path, 'rb') as file:
            reader = PdfReader(file)
            
            # Check encryption
            print(f"Is encrypted: {reader.is_encrypted}")
            
            if reader.is_encrypted:
                print("Attempting to decrypt...")
                try:
                    # Try empty password
                    if reader.decrypt(""):
                        print("✓ Successfully decrypted with empty password")
                    else:
                        print("✗ Failed to decrypt with empty password")
                        print("This PDF requires a password")
                        return False
                except Exception as decrypt_error:
                    print(f"✗ Decryption error: {decrypt_error}")
                    return False
            
            # Check for security handler
            trailer = reader.trailer
            if '/Encrypt' in trailer:
                encrypt_obj = trailer['/Encrypt']
                if hasattr(encrypt_obj, 'get_object'):
                    encrypt_dict = encrypt_obj.get_object()
                else:
                    encrypt_dict = encrypt_obj
                    
                print(f"Encryption dictionary found:")
                if isinstance(encrypt_dict, DictionaryObject):
                    for key, value in encrypt_dict.items():
                        print(f"  {key}: {value}")
                        
                    # Check permissions (P entry)
                    if '/P' in encrypt_dict:
                        p_value = int(encrypt_dict['/P'])
                        print(f"\nPermissions value (P): {p_value}")
                        print(f"Permissions binary: {bin(p_value)}")
                        
                        # Decode permission bits
                        permissions = {
                            'print_low_res': bool(p_value & (1 << 2)),
                            'modify_contents': bool(p_value & (1 << 3)),
                            'extract_text': bool(p_value & (1 << 4)),
                            'add_annotations': bool(p_value & (1 << 5)),
                            'fill_forms': bool(p_value & (1 << 8)),
                            'extract_for_accessibility': bool(p_value & (1 << 9)),
                            'assemble_document': bool(p_value & (1 << 10)),
                            'print_high_res': bool(p_value & (1 << 11))
                        }
                        
                        print("\nDecoded permissions:")
                        for perm, allowed in permissions.items():
                            status = "✓" if allowed else "✗"
                            print(f"  {status} {perm}: {allowed}")
                            
                        return permissions.get('fill_forms', False) and permissions.get('modify_contents', False)
            else:
                print("No encryption dictionary found - PDF is not protected")
                return True
                
    except Exception as e:
        print(f"Error checking PDF security: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_file_operations(file_path: str):
    """Test actual file operations"""
    print(f"\nFile Operations Test for: {file_path}")
    print("=" * 50)
    
    try:
        # Test reading
        print("Testing read access...")
        with open(file_path, 'rb') as f:
            data = f.read(1024)  # Read first 1KB
        print(f"✓ Successfully read {len(data)} bytes")
        
        # Test if we can create a copy
        test_copy_path = file_path.replace('.pdf', '_permission_test.pdf')
        print(f"Testing copy creation to: {test_copy_path}")
        
        import shutil
        shutil.copy2(file_path, test_copy_path)
        print("✓ Successfully created copy")
        
        # Check copy size
        original_size = os.path.getsize(file_path)
        copy_size = os.path.getsize(test_copy_path)
        print(f"Original size: {original_size:,} bytes")
        print(f"Copy size: {copy_size:,} bytes")
        
        if original_size == copy_size:
            print("✓ Copy size matches original")
        else:
            print(f"✗ Copy size differs by {abs(original_size - copy_size):,} bytes")
        
        # Clean up test copy
        os.remove(test_copy_path)
        print("✓ Test copy cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ File operations test failed: {e}")
        return False


def main():
    """Main function"""
    file_path = "fw9.pdf"
    
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} not found")
        sys.exit(1)
    
    # Check file system permissions
    can_read, can_write = check_file_permissions(file_path)
    
    # Check PDF security
    pdf_allows_modification = check_pdf_security(file_path)
    
    # Test file operations
    file_ops_ok = test_file_operations(file_path)
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print(f"File system read access: {'✓' if can_read else '✗'}")
    print(f"File system write access: {'✓' if can_write else '✗'}")
    print(f"PDF allows modification: {'✓' if pdf_allows_modification else '✗'}")
    print(f"File operations work: {'✓' if file_ops_ok else '✗'}")
    
    if can_read and can_write and pdf_allows_modification and file_ops_ok:
        print("\n✓ All permissions look good - the issue may be elsewhere")
    else:
        print("\n✗ Permission issues detected that may affect PDF processing")


if __name__ == "__main__":
    main()
