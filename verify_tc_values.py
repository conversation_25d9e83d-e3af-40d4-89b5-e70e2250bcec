#!/usr/bin/env python3
"""
Verify that Tc values are correctly set in PDF form fields
"""

import sys
from pypdf import PdfReader
from pypdf.generic import DictionaryObject, ArrayObject
import re


def verify_tc_values(pdf_path: str):
    """Verify Tc values in PDF form fields"""
    print(f"Verifying Tc values in: {pdf_path}")
    print("=" * 60)
    
    # Expected Tc values based on letterSpace calculations
    expected_tc_values = {
        'f1_03[0]': None,  # letterSpace 0.0, no Tc expected
        'topmostSubform[0].Page1[0].f1_11[0]': 10.3950,  # letterSpace 1.155 * 9
        'topmostSubform[0].Page1[0].f1_12[0]': 13.8600,  # letterSpace 1.54 * 9
        'topmostSubform[0].Page1[0].f1_13[0]': 9.2403,   # letterSpace 1.0267 * 9
        'topmostSubform[0].Page1[0].f1_14[0]': 13.8600,  # letterSpace 1.54 * 9
        'topmostSubform[0].Page1[0].f1_15[0]': 8.0847    # letterSpace 0.8983 * 9
    }
    
    try:
        with open(pdf_path, 'rb') as file:
            reader = PdfReader(file)
            
            # Check AcroForm
            if '/AcroForm' not in reader.trailer['/Root']:
                print("No AcroForm found in PDF")
                return False
                
            acroform = reader.trailer['/Root']['/AcroForm']
            if '/Fields' not in acroform:
                print("No fields found in AcroForm")
                return False
            
            # Process fields and extract Tc values
            found_tc_values = {}
            process_fields_for_tc(acroform['/Fields'], found_tc_values)
            
            # Verify results
            print("Tc Verification Results:")
            print("-" * 40)
            
            all_correct = True
            for field_name, expected_tc in expected_tc_values.items():
                if field_name in found_tc_values:
                    actual_tc = found_tc_values[field_name]
                    
                    if expected_tc is None:
                        if actual_tc is None:
                            print(f"✓ {field_name}: No Tc (correct)")
                        else:
                            print(f"✗ {field_name}: Expected no Tc, found {actual_tc}")
                            all_correct = False
                    else:
                        if actual_tc is not None:
                            # Allow small floating point differences
                            if abs(actual_tc - expected_tc) < 0.001:
                                print(f"✓ {field_name}: Tc {actual_tc:.4f} (correct)")
                            else:
                                print(f"✗ {field_name}: Expected Tc {expected_tc:.4f}, found {actual_tc:.4f}")
                                all_correct = False
                        else:
                            print(f"✗ {field_name}: Expected Tc {expected_tc:.4f}, found none")
                            all_correct = False
                else:
                    print(f"✗ {field_name}: Field not found")
                    all_correct = False
            
            # Show any additional fields with Tc
            additional_fields = set(found_tc_values.keys()) - set(expected_tc_values.keys())
            if additional_fields:
                print("\nAdditional fields with Tc:")
                for field_name in additional_fields:
                    tc_value = found_tc_values[field_name]
                    if tc_value is not None:
                        print(f"  {field_name}: Tc {tc_value:.4f}")
            
            print("\n" + "=" * 60)
            if all_correct:
                print("✓ All Tc values are correct!")
            else:
                print("✗ Some Tc values are incorrect or missing")
            
            return all_correct
            
    except Exception as e:
        print(f"Error verifying Tc values: {e}")
        import traceback
        traceback.print_exc()
        return False


def process_fields_for_tc(fields, found_tc_values, parent_name=""):
    """Recursively process form fields to extract Tc values from DA strings"""
    if hasattr(fields, 'get_object'):
        fields = fields.get_object()
        
    if not isinstance(fields, (list, ArrayObject)):
        return
    
    for field_ref in fields:
        try:
            if hasattr(field_ref, 'get_object'):
                field = field_ref.get_object()
            else:
                field = field_ref
            
            if not isinstance(field, DictionaryObject):
                continue
            
            # Get field name
            field_name = ""
            if '/T' in field:
                field_name = str(field['/T'])
            
            # Build full name
            if parent_name:
                full_name = f"{parent_name}.{field_name}" if field_name else parent_name
            else:
                full_name = field_name
            
            # Extract Tc value from DA string
            if '/DA' in field:
                da_string = str(field['/DA'])
                tc_value = extract_tc_from_da(da_string)
                
                if field_name:
                    found_tc_values[field_name] = tc_value
                if full_name and full_name != field_name:
                    found_tc_values[full_name] = tc_value
                    
                if tc_value is not None:
                    print(f"Found Tc in field '{field_name}': {tc_value:.4f}")
                    print(f"  DA string: '{da_string}'")
            
            # Process child fields
            if '/Kids' in field:
                process_fields_for_tc(field['/Kids'], found_tc_values, full_name)
                
        except Exception as e:
            print(f"Error processing field: {e}")
            continue


def extract_tc_from_da(da_string: str) -> float:
    """Extract Tc value from DA string"""
    try:
        # Pattern to match Tc value: number followed by Tc
        tc_pattern = r'(\d+\.?\d*)\s+Tc'
        match = re.search(tc_pattern, da_string)
        if match:
            return float(match.group(1))
        else:
            return None
    except Exception as e:
        print(f"Error extracting Tc from DA string: {e}")
        return None


def main():
    """Main function"""
    pdf_path = "out.pdf"
    
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    
    success = verify_tc_values(pdf_path)
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
