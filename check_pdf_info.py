#!/usr/bin/env python3
"""
Check PDF information including encryption status and permissions
"""

import sys
from pypdf import Pdf<PERSON>eader
from pypdf.generic import DictionaryObject, ArrayObject


def check_pdf_info(pdf_path: str):
    """Check PDF information and permissions"""
    try:
        print(f"Analyzing PDF: {pdf_path}")
        print("=" * 50)
        
        with open(pdf_path, 'rb') as file:
            reader = PdfReader(file)
            
            # Basic info
            print(f"Number of pages: {len(reader.pages)}")
            
            # Check encryption
            print(f"Is encrypted: {reader.is_encrypted}")
            if reader.is_encrypted:
                print("Attempting to decrypt with empty password...")
                try:
                    if reader.decrypt(""):
                        print("✓ Successfully decrypted with empty password")
                    else:
                        print("✗ Failed to decrypt with empty password")
                        print("This PDF requires a password")
                        return
                except Exception as e:
                    print(f"✗ Decryption error: {e}")
                    return
            
            # Check metadata
            if hasattr(reader, 'metadata') and reader.metadata:
                print("\nMetadata:")
                for key, value in reader.metadata.items():
                    print(f"  {key}: {value}")
            else:
                print("No metadata found")
            
            # Check AcroForm
            print(f"\nAcroForm analysis:")
            if '/AcroForm' in reader.trailer['/Root']:
                acroform = reader.trailer['/Root']['/AcroForm']
                print("✓ AcroForm found")
                
                if '/Fields' in acroform:
                    fields = acroform['/Fields']
                    if isinstance(fields, (list, ArrayObject)):
                        print(f"  Number of form fields: {len(fields)}")
                    else:
                        print("  Fields structure is not a list")
                else:
                    print("  No fields found in AcroForm")
                    
                # Check other AcroForm properties
                if '/NeedAppearances' in acroform:
                    print(f"  NeedAppearances: {acroform['/NeedAppearances']}")
                if '/DR' in acroform:
                    print("  Default Resources (DR) found")
                if '/DA' in acroform:
                    print(f"  Default Appearance (DA): {acroform['/DA']}")
                if '/Q' in acroform:
                    print(f"  Quadding (Q): {acroform['/Q']}")
                    
            else:
                print("✗ No AcroForm found")
            
            # Try to check if we can modify the PDF
            print(f"\nModification test:")
            try:
                from pypdf import PdfWriter
                writer = PdfWriter()
                
                # Try to copy pages
                for page in reader.pages:
                    writer.add_page(page)
                print("✓ Successfully copied pages to writer")
                
                # Try to copy AcroForm
                if '/AcroForm' in reader.trailer['/Root']:
                    from pypdf.generic import NameObject
                    acroform = reader.trailer['/Root']['/AcroForm']
                    writer._root_object[NameObject('/AcroForm')] = acroform
                    print("✓ Successfully copied AcroForm to writer")
                
                # Try to write to a test file
                test_output = pdf_path.replace('.pdf', '_test_copy.pdf')
                with open(test_output, 'wb') as test_file:
                    writer.write(test_file)
                print(f"✓ Successfully created test copy: {test_output}")
                
                # Clean up test file
                import os
                os.remove(test_output)
                print("✓ Test copy cleaned up")
                
            except Exception as e:
                print(f"✗ Modification test failed: {e}")
                print("  This PDF may be protected against modification")
            
            print("\n" + "=" * 50)
            print("Analysis complete")
            
    except Exception as e:
        print(f"Error analyzing PDF: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main function"""
    pdf_path = "fw9.pdf"
    
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    
    check_pdf_info(pdf_path)


if __name__ == "__main__":
    main()
