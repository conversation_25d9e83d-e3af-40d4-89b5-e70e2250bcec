#!/usr/bin/env python3
"""
Verify that the PDF form fields have been filled correctly
"""

import sys
from pypdf import PdfReader
from pypdf.generic import DictionaryObject, ArrayObject


def verify_pdf_fields(pdf_path):
    """Verify the filled PDF form fields"""
    try:
        reader = PdfReader(pdf_path)
        
        # Check if AcroForm exists
        if '/AcroForm' not in reader.root_object:
            print("No AcroForm found in PDF")
            return
        
        acroform = reader.root_object['/AcroForm']
        
        if '/Fields' not in acroform:
            print("No fields found in AcroForm")
            return
        
        print(f"Verifying filled PDF: {pdf_path}")
        print("-" * 50)
        
        # Expected values
        expected_values = {
            'f1_03[0]': '1',
            'topmostSubform[0].Page1[0].f1_11[0]': '231',
            'topmostSubform[0].Page1[0].f1_12[0]': '22',
            'topmostSubform[0].Page1[0].f1_13[0]': '2024',
            'topmostSubform[0].Page1[0].f1_14[0]': '12',
            'topmostSubform[0].Page1[0].f1_15[0]': '1234567'
        }
        
        # Process fields recursively
        found_fields = {}
        process_fields_recursive(acroform['/Fields'], found_fields)
        
        # Check results
        print("Field verification results:")
        print("-" * 30)
        
        all_correct = True
        for field_name, expected_value in expected_values.items():
            if field_name in found_fields:
                actual_value = found_fields[field_name]
                if actual_value == expected_value:
                    print(f"✓ {field_name}: '{actual_value}' (correct)")
                else:
                    print(f"✗ {field_name}: expected '{expected_value}', got '{actual_value}'")
                    all_correct = False
            else:
                print(f"✗ {field_name}: field not found")
                all_correct = False
        
        # Show any additional fields found
        additional_fields = set(found_fields.keys()) - set(expected_values.keys())
        if additional_fields:
            print("\nAdditional fields found:")
            for field_name in additional_fields:
                print(f"  {field_name}: '{found_fields[field_name]}'")
        
        print("\n" + "=" * 50)
        if all_correct:
            print("✓ All expected fields have correct values!")
        else:
            print("✗ Some fields have incorrect or missing values")
        
        return all_correct
        
    except Exception as e:
        print(f"Error verifying PDF: {e}")
        import traceback
        traceback.print_exc()
        return False


def process_fields_recursive(fields, found_fields, parent_name=""):
    """Recursively process form fields to extract values"""
    if not isinstance(fields, (list, ArrayObject)):
        return
    
    for field_ref in fields:
        try:
            # Resolve field reference
            if hasattr(field_ref, 'get_object'):
                field = field_ref.get_object()
            else:
                field = field_ref
            
            if not isinstance(field, DictionaryObject):
                continue
            
            # Get field name
            field_name = ""
            if '/T' in field:
                field_name = str(field['/T'])
            
            # Build full name
            if parent_name:
                full_name = f"{parent_name}.{field_name}" if field_name else parent_name
            else:
                full_name = field_name
            
            # Get field value
            if '/V' in field:
                value = str(field['/V'])
                if field_name:
                    found_fields[field_name] = value
                if full_name and full_name != field_name:
                    found_fields[full_name] = value
            
            # Process child fields if they exist
            if '/Kids' in field:
                process_fields_recursive(field['/Kids'], found_fields, full_name)
                
        except Exception as e:
            print(f"Error processing field: {e}")
            continue


def main():
    """Main function"""
    pdf_path = "out.pdf"
    
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    
    success = verify_pdf_fields(pdf_path)
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
