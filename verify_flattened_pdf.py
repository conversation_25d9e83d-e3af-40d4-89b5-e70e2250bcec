#!/usr/bin/env python3
"""
Verify that flattened PDF contains the expected text content
"""

import sys
from pypdf import PdfReader
import re


def extract_text_from_pdf(pdf_path: str):
    """Extract all text from PDF pages"""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PdfReader(file)
            
            all_text = ""
            for page_num, page in enumerate(reader.pages):
                try:
                    page_text = page.extract_text()
                    all_text += f"\n--- Page {page_num + 1} ---\n"
                    all_text += page_text
                except Exception as e:
                    print(f"Error extracting text from page {page_num + 1}: {e}")
            
            return all_text
            
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return ""


def verify_flattened_content(pdf_path: str):
    """Verify that flattened PDF contains expected field values"""
    print(f"Verifying flattened PDF content: {pdf_path}")
    print("=" * 60)
    
    # Expected values from numberOfCells fields
    expected_values = [
        "1",        # f1_03[0]
        "231",      # f1_11[0] 
        "22",       # f1_12[0]
        "2024",     # f1_13[0]
        "12",       # f1_14[0]
        "1234567"   # f1_15[0]
    ]
    
    # Extract text from PDF
    pdf_text = extract_text_from_pdf(pdf_path)
    
    if not pdf_text:
        print("Failed to extract text from PDF")
        return False
    
    print("Extracted text from PDF:")
    print("-" * 40)
    print(pdf_text)
    print("-" * 40)
    
    # Check for expected values
    print("\nChecking for expected field values:")
    print("-" * 40)
    
    found_values = []
    missing_values = []
    
    for value in expected_values:
        if value in pdf_text:
            found_values.append(value)
            print(f"✓ Found: '{value}'")
        else:
            missing_values.append(value)
            print(f"✗ Missing: '{value}'")
    
    # Summary
    print("\n" + "=" * 60)
    print("VERIFICATION SUMMARY")
    print("=" * 60)
    print(f"Expected values: {len(expected_values)}")
    print(f"Found values: {len(found_values)}")
    print(f"Missing values: {len(missing_values)}")
    
    if missing_values:
        print(f"\nMissing values: {missing_values}")
        print("\n✗ Flattening did not preserve all field values")
        return False
    else:
        print("\n✓ All expected field values found in flattened PDF!")
        return True


def compare_pdfs(original_pdf: str, flattened_pdf: str):
    """Compare original and flattened PDFs"""
    print(f"\nComparing PDFs:")
    print(f"Original: {original_pdf}")
    print(f"Flattened: {flattened_pdf}")
    print("-" * 60)
    
    # Check if original has form fields
    try:
        with open(original_pdf, 'rb') as file:
            reader = PdfReader(file)
            has_forms = '/AcroForm' in reader.trailer.get('/Root', {})
            print(f"Original PDF has forms: {has_forms}")
    except Exception as e:
        print(f"Error checking original PDF: {e}")
    
    # Check if flattened has form fields
    try:
        with open(flattened_pdf, 'rb') as file:
            reader = PdfReader(file)
            has_forms = '/AcroForm' in reader.trailer.get('/Root', {})
            print(f"Flattened PDF has forms: {has_forms}")
            
            if has_forms:
                print("WARNING: Flattened PDF still contains form fields")
            else:
                print("✓ Flattened PDF has no form fields (properly flattened)")
                
    except Exception as e:
        print(f"Error checking flattened PDF: {e}")


def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python verify_flattened_pdf.py <flattened.pdf> [original.pdf]")
        flattened_pdf = "flattened.pdf"
        original_pdf = "out.pdf"
    else:
        flattened_pdf = sys.argv[1]
        original_pdf = sys.argv[2] if len(sys.argv) > 2 else "out.pdf"
    
    # Verify flattened content
    success = verify_flattened_content(flattened_pdf)
    
    # Compare with original if available
    try:
        compare_pdfs(original_pdf, flattened_pdf)
    except Exception as e:
        print(f"Could not compare PDFs: {e}")
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
