PDF Form Field Tc (Character Spacing) Modifications Summary
============================================================

Source PDF: fw9.pdf
Total fields with numberOfCells: 6

Field Modifications:
----------------------------------------
Field Name: topmostSubform[0].Page1[0].topmostSubform[0].Page1[0].f1_11[0]
  numberOfCells: 3
  Field Width: 43.20 pt
  Calculated letterSpace: 1.0500 em
  Tc value to add: 9.4500 pt
  Original DA: //Helvetica 9 Tf 0 g
  Modified DA: //Helvetica 9 Tf 0 g 9.4500 Tc

Field Name: topmostSubform[0].Page1[0].topmostSubform[0].Page1[0].f1_12[0]
  numberOfCells: 2
  Field Width: 28.80 pt
  Calculated letterSpace: 1.0500 em
  Tc value to add: 9.4500 pt
  Original DA: //Helvetica 9 Tf 0 g
  Modified DA: //Helvetica 9 Tf 0 g 9.4500 Tc

Field Name: topmostSubform[0].Page1[0].topmostSubform[0].Page1[0].f1_13[0]
  numberOfCells: 4
  Field Width: 57.60 pt
  Calculated letterSpace: 1.0500 em
  Tc value to add: 9.4500 pt
  Original DA: //Helvetica 9 Tf 0 g
  Modified DA: //Helvetica 9 Tf 0 g 9.4500 Tc

Field Name: topmostSubform[0].Page1[0].topmostSubform[0].Page1[0].f1_14[0]
  numberOfCells: 2
  Field Width: 28.80 pt
  Calculated letterSpace: 1.0500 em
  Tc value to add: 9.4500 pt
  Original DA: //Helvetica 9 Tf 0 g
  Modified DA: //Helvetica 9 Tf 0 g 9.4500 Tc

Field Name: topmostSubform[0].Page1[0].topmostSubform[0].Page1[0].f1_15[0]
  numberOfCells: 7
  Field Width: 100.80 pt
  Calculated letterSpace: 1.0500 em
  Tc value to add: 9.4500 pt
  Original DA: //Helvetica 9 Tf 0 g
  Modified DA: //Helvetica 9 Tf 0 g 9.4500 Tc


Instructions for Manual Application:
----------------------------------------
To manually apply these Tc values to PDF form fields:
1. Open the PDF in a PDF editor (e.g., Adobe Acrobat)
2. Access the form field properties
3. In the Default Appearance (DA) string, add the Tc value
4. The Tc operator sets character spacing in text units
5. Format: 'existing_da_string {tc_value} Tc'

Example:
Original DA: '/Helvetica 9 Tf 0 g'
Modified DA: '/Helvetica 9 Tf 0 g 10.3950 Tc'
