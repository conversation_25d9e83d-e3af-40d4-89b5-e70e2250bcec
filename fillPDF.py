#!/usr/bin/env python3
"""
PDF Form Field Filling Script
Reads numberOfCells_fields.json and fills PDF form fields with values
Uses PyPDF v5.6 for better PDF handling
"""

import json
import sys
from typing import Dict, List, Any, Optional
import pypdf
from pypdf import PdfWriter, PdfReader
from pypdf.generic import DictionaryObject, ArrayObject, IndirectObject, TextStringObject, NameObject


class PDFFormFiller:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.reader = None
        self.writer = None
        self.file_handle = None

    def open_pdf(self) -> bool:
        """Open PDF file for reading"""
        try:
            self.file_handle = open(self.pdf_path, 'rb')
            self.reader = PdfReader(self.file_handle)

            # Check if PDF is encrypted
            if self.reader.is_encrypted:
                print("PDF is encrypted. Attempting to decrypt with empty password...")
                try:
                    # Try to decrypt with empty password
                    if not self.reader.decrypt(""):
                        print("Failed to decrypt PDF with empty password")
                        print("This PDF requires a password to edit")
                        return False
                    else:
                        print("Successfully decrypted PDF with empty password")
                except Exception as decrypt_error:
                    print(f"Decryption failed: {decrypt_error}")
                    return False

            # Check PDF permissions
            if hasattr(self.reader, 'metadata') and self.reader.metadata:
                print("PDF metadata found")

            # Check if we can modify the PDF
            try:
                # Method 1: Try using clone_reader_document_root for complete copy
                print("Attempting complete PDF clone...")
                self.writer = PdfWriter()
                self.writer.clone_reader_document_root(self.reader)
                print("Successfully cloned entire PDF document")
                return True

            except Exception as clone_error:
                print(f"Clone method failed: {clone_error}")

                # Method 2: Fallback to manual copying
                try:
                    print("Falling back to manual page and resource copying...")
                    self.writer = PdfWriter()

                    # Copy all pages to writer
                    for page in self.reader.pages:
                        self.writer.add_page(page)

                    # Copy document catalog items
                    root = self.reader.trailer['/Root']

                    # Copy AcroForm
                    if '/AcroForm' in root:
                        acroform = root['/AcroForm']
                        self.writer._root_object[NameObject('/AcroForm')] = acroform
                        print("Successfully copied AcroForm to writer")
                    else:
                        print("AcroForm not found in PDF file")
                        return False

                    # Copy other important catalog entries
                    important_keys = ['/Names', '/Dests', '/ViewerPreferences', '/PageLabels',
                                    '/Threads', '/OpenAction', '/AA', '/URI', '/Metadata']

                    for key in important_keys:
                        if key in root:
                            try:
                                self.writer._root_object[NameObject(key)] = root[key]
                                print(f"Copied {key} to writer")
                            except Exception as copy_key_error:
                                print(f"Failed to copy {key}: {copy_key_error}")

                    print("Successfully copied PDF content with manual method")
                    return True

                except Exception as manual_error:
                    print(f"Manual copy method also failed: {manual_error}")
                    print("PDF may be protected against modification")
                    return False

        except Exception as e:
            print(f"Error opening PDF file: {e}")
            return False

    def close_pdf(self):
        """Close PDF file"""
        if hasattr(self, 'file_handle') and self.file_handle:
            self.file_handle.close()

    def check_pdf_permissions(self) -> Dict[str, bool]:
        """Check PDF permissions and restrictions"""
        permissions = {
            'can_print': True,
            'can_modify': True,
            'can_copy': True,
            'can_annotate': True,
            'can_fill_forms': True,
            'can_extract': True,
            'can_assemble': True,
            'can_print_high_quality': True
        }

        try:
            if self.reader.is_encrypted:
                # Check user permissions if PDF is encrypted
                # Note: pypdf may not expose all permission details
                print("PDF is encrypted - some operations may be restricted")

            # Try to access trailer and root to check basic accessibility
            if '/Root' in self.reader.trailer:
                root = self.reader.trailer['/Root']
                if '/AcroForm' in root:
                    acroform = root['/AcroForm']
                    # Check if form filling is allowed
                    if '/NeedAppearances' in acroform:
                        print(f"NeedAppearances flag: {acroform['/NeedAppearances']}")

            return permissions

        except Exception as e:
            print(f"Error checking PDF permissions: {e}")
            # Assume restricted if we can't check
            return {key: False for key in permissions}

    def resolve_reference(self, obj):
        """Resolve indirect object references"""
        if isinstance(obj, IndirectObject):
            return obj.get_object()
        return obj

    def load_field_data(self, json_path: str) -> Dict[str, Any]:
        """Load field data from JSON file"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"Error loading JSON file: {e}")
            return {}

    def extract_field_values(self, json_data: Dict[str, Any]) -> tuple[Dict[str, str], Dict[str, Dict[str, float]]]:
        """Extract field names, values and comb field data from JSON data"""
        field_values = {}
        field_comb_data = {}

        if 'fields' in json_data:
            for field in json_data['fields']:
                if 'value' in field and field['value']:
                    # Try both name and full_name as keys
                    field_name = field.get('name', '')
                    full_name = field.get('full_name', '')
                    value = str(field['value'])

                    if field_name:
                        field_values[field_name] = value
                    if full_name and full_name != field_name:
                        field_values[full_name] = value

                    print(f"Found field to fill: {field_name} = '{value}'")

                    # Extract comb field data for numberOfCells fields
                    if 'numberOfCells' in field and field['numberOfCells']:
                        comb_info = {
                            'numberOfCells': field.get('numberOfCells', 1),
                            'field_width': field.get('field_width', 0),
                            'letterSpace': field.get('letterSpace', 0),
                            'value_length': len(value)
                        }

                        if field_name:
                            field_comb_data[field_name] = comb_info
                        if full_name and full_name != field_name:
                            field_comb_data[full_name] = comb_info

                        print(f"Found comb field {field_name}: {field['numberOfCells']} cells, width={field.get('field_width', 0):.1f}, letterSpace={field.get('letterSpace', 0)}")

        return field_values, field_comb_data

    def fill_form_fields(self, field_values: Dict[str, str], field_comb_data: Dict[str, Dict[str, float]] = None) -> bool:
        """Fill form fields with values and comb field data using multiple approaches"""
        try:
            if field_comb_data is None:
                field_comb_data = {}

            # Method 1: Try to fill fields in the writer
            success_writer = self._fill_fields_in_writer(field_values, field_comb_data)

            # Method 2: Also try to fill fields in the reader (for better compatibility)
            success_reader = self._fill_fields_in_reader(field_values, field_comb_data)

            if success_writer or success_reader:
                print("Form fields filled successfully")
                return True
            else:
                print("Failed to fill form fields with any method")
                return False

        except Exception as e:
            print(f"Error filling form fields: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _fill_fields_in_writer(self, field_values: Dict[str, str], field_comb_data: Dict[str, Dict[str, float]]) -> bool:
        """Fill form fields in the writer object"""
        try:
            # Get AcroForm from writer
            if '/AcroForm' not in self.writer._root_object:
                print("No AcroForm found in writer")
                return False

            acroform = self.writer._root_object['/AcroForm']
            if '/Fields' not in acroform:
                print("No fields found in AcroForm (writer)")
                return False

            print("Filling fields in writer...")
            # Update form fields
            self._fill_fields_recursive(acroform['/Fields'], field_values, "writer", "", field_comb_data)
            return True

        except Exception as e:
            print(f"Error filling fields in writer: {e}")
            return False

    def _fill_fields_in_reader(self, field_values: Dict[str, str], field_comb_data: Dict[str, Dict[str, float]]) -> bool:
        """Fill form fields in the reader object"""
        try:
            # Get AcroForm from reader
            if '/AcroForm' not in self.reader.trailer['/Root']:
                print("No AcroForm found in reader")
                return False

            acroform = self.reader.trailer['/Root']['/AcroForm']
            if '/Fields' not in acroform:
                print("No fields found in AcroForm (reader)")
                return False

            print("Filling fields in reader...")
            # Update form fields
            self._fill_fields_recursive(acroform['/Fields'], field_values, "reader", "", field_comb_data)
            return True

        except Exception as e:
            print(f"Error filling fields in reader: {e}")
            return False

    def _fill_fields_recursive(self, fields, field_values: Dict[str, str], source: str = "", parent_name: str = "", field_comb_data: Dict[str, Dict[str, float]] = None):
        """Recursively fill form fields with values and comb field data"""
        if field_comb_data is None:
            field_comb_data = {}

        fields = self.resolve_reference(fields)
        if not isinstance(fields, (list, ArrayObject)):
            return

        for field_ref in fields:
            try:
                # Resolve field reference
                field = self.resolve_reference(field_ref)

                if not isinstance(field, DictionaryObject):
                    continue

                # Get field name
                field_name = ""
                if '/T' in field:
                    field_name = str(field['/T'])

                # Build full name
                if parent_name:
                    full_name = f"{parent_name}.{field_name}" if field_name else parent_name
                else:
                    full_name = field_name

                # Check if we have a value for this field
                value_to_set = None
                if field_name in field_values:
                    value_to_set = field_values[field_name]
                elif full_name in field_values:
                    value_to_set = field_values[full_name]

                # Check if we have comb data for this field
                comb_info = None
                if field_name in field_comb_data:
                    comb_info = field_comb_data[field_name]
                elif full_name in field_comb_data:
                    comb_info = field_comb_data[full_name]

                # Set the field value
                if value_to_set is not None:
                    # Try multiple ways to set the value
                    try:
                        # Method 1: Direct assignment with TextStringObject
                        field[NameObject('/V')] = TextStringObject(value_to_set)
                        print(f"[{source}] Set field '{field_name}' (full: '{full_name}') to value: '{value_to_set}'")

                        # Also set default value for better compatibility
                        field[NameObject('/DV')] = TextStringObject(value_to_set)

                    except Exception as set_error1:
                        print(f"[{source}] Method 1 failed: {set_error1}")

                        # Method 2: Try with update method
                        try:
                            field.update({NameObject('/V'): TextStringObject(value_to_set)})
                            field.update({NameObject('/DV'): TextStringObject(value_to_set)})
                            print(f"[{source}] Set field '{field_name}' using update method")
                        except Exception as set_error2:
                            print(f"[{source}] Method 2 failed: {set_error2}")

                            # Method 3: Try direct string assignment
                            try:
                                field['/V'] = value_to_set
                                field['/DV'] = value_to_set
                                print(f"[{source}] Set field '{field_name}' using direct string assignment")
                            except Exception as set_error3:
                                print(f"[{source}] All methods failed for field '{field_name}': {set_error3}")

                # Set character spacing for comb fields
                if comb_info is not None:
                    self._set_comb_field_spacing(field, field_name, comb_info, value_to_set or "", source)

                # Generate appearance for better flattening compatibility
                if value_to_set is not None:
                    self._generate_field_appearance(field, field_name, value_to_set, source)

                    # Try to set NeedAppearances flag
                    try:
                        if hasattr(self, 'writer') and self.writer and '/AcroForm' in self.writer._root_object:
                            acroform = self.writer._root_object['/AcroForm']
                            acroform[NameObject('/NeedAppearances')] = True
                    except Exception as appearance_error:
                        print(f"[{source}] Could not set NeedAppearances: {appearance_error}")

                # Process child fields if they exist
                if '/Kids' in field:
                    self._fill_fields_recursive(field['/Kids'], field_values, source, full_name, field_comb_data)

            except Exception as e:
                print(f"[{source}] Error processing field: {e}")
                continue

    def _set_comb_field_spacing(self, field: DictionaryObject, field_name: str, comb_info: Dict[str, float], value: str, source: str):
        """Set character spacing (Tc) for comb fields based on numberOfCells and field width"""
        try:
            # Check if field has a DA (Default Appearance) string
            if '/DA' not in field:
                print(f"[{source}] No DA string found for field '{field_name}' - cannot set Tc")
                return

            # Get current DA string and extract font size
            current_da = str(field['/DA'])
            font_size = self._extract_font_size_from_da(current_da)

            # Extract comb field parameters
            numberOfCells = comb_info.get('numberOfCells', 1)
            field_width = comb_info.get('field_width', 0)
            value_length = len(value)

            print(f"[{source}] Calculating Tc for comb field '{field_name}':")
            print(f"  numberOfCells: {numberOfCells}")
            print(f"  field_width: {field_width:.2f}")
            print(f"  value_length: {value_length}")
            print(f"  font_size: {font_size}")

            if field_width <= 0 or numberOfCells <= 0:
                print(f"[{source}] Invalid field dimensions for '{field_name}', skipping Tc calculation")
                return

            # Calculate character spacing for comb fields
            # For comb fields, characters should be evenly distributed across the field width
            # Each cell should have equal width: cell_width = field_width / numberOfCells
            cell_width = field_width / numberOfCells

            # Estimate character width (more accurate for typical PDF fonts)
            # For Helvetica and similar fonts, character width is approximately 0.55 * font_size
            char_width = font_size * 0.55

            # Calculate the spacing needed between characters
            # For comb fields, we want characters centered in their cells
            # The key insight: Tc controls the spacing between character origins
            # To center characters in cells, we need: char_spacing = cell_width - char_width

            if value_length <= numberOfCells:
                # Normal case: we have enough cells for all characters
                # Each character should be centered in its cell
                # The distance between character centers should equal cell_width
                tc_value = cell_width - char_width

                # Ensure minimum spacing (prevent negative values)
                tc_value = max(0.0, tc_value)

                # For better alignment in flattened PDFs, add a small adjustment
                # This compensates for font rendering differences during flattening
                if numberOfCells >= 7:
                    # For larger numberOfCells, add extra spacing to prevent compression
                    tc_value *= 1.04  # 15% increase for better spacing
                elif numberOfCells >= 4:
                    # Medium adjustment for medium-sized fields
                    tc_value *= 1.04  # 8% increase

            else:
                # More characters than cells - compress spacing
                available_width = field_width - (value_length * char_width)
                if value_length > 1:
                    tc_value = max(0, available_width / (value_length - 1))
                else:
                    tc_value = 0

            print(f"  calculated cell_width: {cell_width:.2f}")
            print(f"  estimated char_width: {char_width:.2f}")
            print(f"  calculated Tc: {tc_value:.4f}")

            # Calculate text indentation (0.3 character spacing units to the right)
            # This helps center the text better within each cell
            indent_offset = tc_value * 1  # 0.3 times the character spacing

            # Apply the Tc value and Td offset to the DA string
            if tc_value > 0:
                # Check if Tc is already in the DA string
                import re
                if ' Tc' in current_da:
                    # Replace existing Tc value
                    tc_pattern = r'(\d+\.?\d*)\s+Tc'
                    new_da = re.sub(tc_pattern, f'{tc_value:.4f} Tc', current_da)
                    print(f"[{source}] Replaced existing Tc in DA for field '{field_name}': {tc_value:.4f}")
                else:
                    # Add Tc to the end of DA string
                    new_da = f"{current_da} {tc_value:.4f} Tc"
                    print(f"[{source}] Added Tc to DA for field '{field_name}': {tc_value:.4f}")

                # Add Td operator for text positioning (horizontal offset) if not already present
                # Td moves the text starting position by the specified offset
                if indent_offset > 0 and ' Td' not in new_da:
                    new_da = f"{new_da} {indent_offset:.4f} 0 Td"
                    print(f"[{source}] Added Td offset for field '{field_name}': {indent_offset:.4f} points right")
                elif ' Td' in new_da:
                    # Replace existing Td value
                    td_pattern = r'(\d+\.?\d*)\s+0\s+Td'
                    new_da = re.sub(td_pattern, f'{indent_offset:.4f} 0 Td', new_da)
                    print(f"[{source}] Replaced existing Td offset for field '{field_name}': {indent_offset:.4f} points right")

                # Update the DA string
                field[NameObject('/DA')] = TextStringObject(new_da)
                print(f"[{source}] Updated DA string for '{field_name}': '{new_da}'")
            else:
                print(f"[{source}] Tc value is 0 or negative, not setting for field '{field_name}'")

        except Exception as e:
            print(f"[{source}] Error setting comb field spacing for field '{field_name}': {e}")

    def _set_field_letter_spacing(self, field: DictionaryObject, field_name: str, letter_space: float, source: str):
        """Set letter spacing (Tc) for a field by modifying its DA string (legacy method)"""
        try:
            # Check if field has a DA (Default Appearance) string
            if '/DA' not in field:
                print(f"[{source}] No DA string found for field '{field_name}' - cannot set Tc")
                return

            # Get current DA string
            current_da = str(field['/DA'])

            # Extract font size from DA string (default to 9 if not found)
            font_size = self._extract_font_size_from_da(current_da)

            # Convert letterSpace (in em units) to Tc value (in points)
            # Tc value = letterSpace * font_size
            tc_value = letter_space * font_size

            # Check if Tc is already in the DA string
            if ' Tc' in current_da:
                # Replace existing Tc value
                import re
                # Pattern to match existing Tc value: number followed by Tc
                tc_pattern = r'(\d+\.?\d*)\s+Tc'
                new_da = re.sub(tc_pattern, f'{tc_value:.4f} Tc', current_da)
                print(f"[{source}] Replaced existing Tc in DA for field '{field_name}': {tc_value:.4f}")
            else:
                # Add Tc to the end of DA string
                new_da = f"{current_da} {tc_value:.4f} Tc"
                print(f"[{source}] Added Tc to DA for field '{field_name}': {tc_value:.4f}")

            # Update the DA string
            field[NameObject('/DA')] = TextStringObject(new_da)
            print(f"[{source}] Updated DA string for '{field_name}': '{new_da}'")

        except Exception as e:
            print(f"[{source}] Error setting letter spacing for field '{field_name}': {e}")

    def _extract_font_size_from_da(self, da_string: str) -> float:
        """Extract font size from DA string, default to 9.0 if not found"""
        try:
            import re
            # Pattern to match font size: number followed by Tf
            font_size_pattern = r'(\d+\.?\d*)\s+Tf'
            match = re.search(font_size_pattern, da_string)
            if match:
                return float(match.group(1))
            else:
                print(f"Font size not found in DA string '{da_string}', using default 9.0")
                return 9.0
        except Exception as e:
            print(f"Error extracting font size from DA string: {e}")
            return 9.0

    def _generate_field_appearance(self, field: DictionaryObject, field_name: str, value: str, source: str):
        """Generate appearance stream for field to ensure proper flattening"""
        try:
            # Clear existing appearance to force regeneration
            if '/AP' in field:
                field[NameObject('/AP')] = DictionaryObject()
                print(f"[{source}] Cleared existing appearance for field '{field_name}'")

            # Set flags to ensure appearance generation
            # Remove ReadOnly flag if present to allow appearance generation
            if '/Ff' in field:
                flags = int(field['/Ff'])
                # Remove ReadOnly flag (bit 0) if set
                if flags & 1:
                    new_flags = flags & ~1
                    field[NameObject('/Ff')] = new_flags
                    print(f"[{source}] Removed ReadOnly flag for field '{field_name}'")

            # For text fields, ensure proper formatting
            if '/FT' in field and str(field['/FT']) == '/Tx':
                # Ensure field has proper quadding (alignment)
                if '/Q' not in field:
                    field[NameObject('/Q')] = 0  # Left aligned

                # For comb fields, ensure proper setup
                if '/Ff' in field:
                    flags = int(field['/Ff'])
                    # Check if it's a comb field (bit 24 set)
                    if flags & (1 << 24):  # Comb flag
                        print(f"[{source}] Field '{field_name}' is a comb field")

                        # Ensure MaxLen is set for comb fields
                        if '/MaxLen' not in field:
                            # Try to determine from numberOfCells or value length
                            max_len = len(value) if value else 1
                            field[NameObject('/MaxLen')] = max_len
                            print(f"[{source}] Set MaxLen to {max_len} for comb field '{field_name}'")

            print(f"[{source}] Generated appearance setup for field '{field_name}'")

        except Exception as e:
            print(f"[{source}] Error generating appearance for field '{field_name}': {e}")

    def _configure_acroform_for_flattening(self, acroform: DictionaryObject):
        """Configure AcroForm properties for better flattening compatibility"""
        try:
            from pypdf.generic import BooleanObject

            # Set NeedAppearances to False - this forces the PDF to have proper appearances
            # When True, viewers generate appearances, but Ghostscript may not handle this
            acroform[NameObject('/NeedAppearances')] = BooleanObject(False)
            print("Set NeedAppearances to False for better flattening compatibility")

            # Ensure SigFlags is properly set if it exists
            if '/SigFlags' in acroform:
                print("SigFlags found in AcroForm")

            # Set XFA to empty if it exists (XFA can interfere with flattening)
            if '/XFA' in acroform:
                print("Warning: XFA found in AcroForm - this may affect flattening")
                # Don't remove XFA as it might be needed, but note its presence

            print("Configured AcroForm for flattening compatibility")

        except Exception as e:
            print(f"Error configuring AcroForm for flattening: {e}")

    def save_pdf(self, output_path: str) -> bool:
        """Save the filled PDF to output file with complete structure preservation"""

        # Method 1: Try using clone_reader_document_root for complete preservation
        try:
            print("Attempting to save with complete structure preservation...")

            # Create a new writer and clone the entire document
            complete_writer = PdfWriter()
            complete_writer.clone_reader_document_root(self.reader)

            # Now we need to update the form fields in the cloned structure
            if '/AcroForm' in complete_writer._root_object:
                acroform = complete_writer._root_object['/AcroForm']
                if '/Fields' in acroform:
                    # Extract field values and comb data that were set
                    field_values = self._extract_set_field_values()
                    field_comb_data = self._extract_set_comb_data()
                    if field_values:
                        print(f"Applying {len(field_values)} field values to cloned PDF...")
                        if field_comb_data:
                            print(f"Applying {len(field_comb_data)} comb field spacing values to cloned PDF...")
                        self._apply_field_values_to_writer(complete_writer, field_values, field_comb_data)

                # Set AcroForm properties for better flattening compatibility
                self._configure_acroform_for_flattening(acroform)

            # Write the complete PDF
            with open(output_path, 'wb') as output_file:
                complete_writer.write(output_file)

            print(f"Successfully saved complete PDF to: {output_path}")
            return True

        except Exception as e1:
            print(f"Complete preservation method failed: {e1}")

        # Method 2: Try standard write (may lose some structure)
        try:
            print("Trying standard write method...")
            with open(output_path, 'wb') as output_file:
                self.writer.write(output_file)

            print(f"Successfully saved PDF (may have reduced structure) to: {output_path}")
            print("WARNING: Some PDF structure elements may be missing")
            return True

        except Exception as e2:
            print(f"Standard save method also failed: {e2}")

        # Method 3: Try copying original and warn about limitations
        try:
            import shutil
            print("Trying fallback: copying original PDF...")
            shutil.copy2(self.pdf_path, output_path)

            print(f"Fallback: copied original PDF to {output_path}")
            print("WARNING: Field values were set in memory but may not be visible in the output PDF")
            print("This PDF may be protected against modification")
            print("Consider using a different PDF tool or removing protection first")
            return True

        except Exception as e3:
            print(f"All save methods failed: {e3}")
            import traceback
            traceback.print_exc()
            return False

    def _extract_set_field_values(self) -> Dict[str, str]:
        """Extract field values that were set during the filling process"""
        field_values = {}

        try:
            # Get AcroForm from reader (where we set the values)
            if '/AcroForm' in self.reader.trailer['/Root']:
                acroform = self.reader.trailer['/Root']['/AcroForm']
                if '/Fields' in acroform:
                    self._extract_values_recursive(acroform['/Fields'], field_values)
        except Exception as e:
            print(f"Error extracting field values: {e}")

        return field_values

    def _extract_set_comb_data(self) -> Dict[str, Dict[str, float]]:
        """Extract comb field data that was processed during filling"""
        # This method extracts comb field data from the original JSON data
        # since we need to preserve this information for the cloned PDF
        field_comb_data = {}

        try:
            # Re-read the JSON data to get comb field data
            json_data = self.load_field_data("numberOfCells_fields.json")
            if 'fields' in json_data:
                for field in json_data['fields']:
                    if 'numberOfCells' in field and field['numberOfCells']:
                        field_name = field.get('name', '')
                        full_name = field.get('full_name', '')
                        value = str(field.get('value', ''))

                        comb_info = {
                            'numberOfCells': field.get('numberOfCells', 1),
                            'field_width': field.get('field_width', 0),
                            'letterSpace': field.get('letterSpace', 0),
                            'value_length': len(value)
                        }

                        if field_name:
                            field_comb_data[field_name] = comb_info
                        if full_name and full_name != field_name:
                            field_comb_data[full_name] = comb_info
        except Exception as e:
            print(f"Error extracting comb field data: {e}")

        return field_comb_data

    def _extract_values_recursive(self, fields, field_values: Dict[str, str], parent_name: str = ""):
        """Recursively extract field values"""
        fields = self.resolve_reference(fields)
        if not isinstance(fields, (list, ArrayObject)):
            return

        for field_ref in fields:
            try:
                field = self.resolve_reference(field_ref)
                if not isinstance(field, DictionaryObject):
                    continue

                # Get field name
                field_name = ""
                if '/T' in field:
                    field_name = str(field['/T'])

                # Build full name
                if parent_name:
                    full_name = f"{parent_name}.{field_name}" if field_name else parent_name
                else:
                    full_name = field_name

                # Get field value if it exists
                if '/V' in field:
                    value = str(field['/V'])
                    if field_name:
                        field_values[field_name] = value
                    if full_name and full_name != field_name:
                        field_values[full_name] = value

                # Process child fields
                if '/Kids' in field:
                    self._extract_values_recursive(field['/Kids'], field_values, full_name)

            except Exception as e:
                print(f"Error extracting field value: {e}")
                continue

    def _apply_field_values_to_writer(self, writer: PdfWriter, field_values: Dict[str, str], field_comb_data: Dict[str, Dict[str, float]] = None):
        """Apply field values and comb data to a writer object"""
        if field_comb_data is None:
            field_comb_data = {}

        try:
            if '/AcroForm' in writer._root_object:
                acroform = writer._root_object['/AcroForm']
                if '/Fields' in acroform:
                    self._apply_values_recursive(acroform['/Fields'], field_values, "", field_comb_data)
        except Exception as e:
            print(f"Error applying field values to writer: {e}")

    def _apply_values_recursive(self, fields, field_values: Dict[str, str], parent_name: str = "", field_comb_data: Dict[str, Dict[str, float]] = None):
        """Recursively apply field values and comb data"""
        if field_comb_data is None:
            field_comb_data = {}

        fields = self.resolve_reference(fields)
        if not isinstance(fields, (list, ArrayObject)):
            return

        for field_ref in fields:
            try:
                field = self.resolve_reference(field_ref)
                if not isinstance(field, DictionaryObject):
                    continue

                # Get field name
                field_name = ""
                if '/T' in field:
                    field_name = str(field['/T'])

                # Build full name
                if parent_name:
                    full_name = f"{parent_name}.{field_name}" if field_name else parent_name
                else:
                    full_name = field_name

                # Apply field value if we have one
                value_to_set = None
                if field_name in field_values:
                    value_to_set = field_values[field_name]
                elif full_name in field_values:
                    value_to_set = field_values[full_name]

                if value_to_set is not None:
                    try:
                        field[NameObject('/V')] = TextStringObject(value_to_set)
                        field[NameObject('/DV')] = TextStringObject(value_to_set)  # Default value
                        print(f"Applied field value: {field_name} = '{value_to_set}'")

                        # Generate appearance for flattening compatibility
                        self._generate_field_appearance(field, field_name, value_to_set, "cloned")

                    except Exception as set_error:
                        print(f"Failed to apply field value for {field_name}: {set_error}")

                # Apply comb field spacing if we have one
                comb_info = None
                if field_name in field_comb_data:
                    comb_info = field_comb_data[field_name]
                elif full_name in field_comb_data:
                    comb_info = field_comb_data[full_name]

                if comb_info is not None:
                    self._set_comb_field_spacing(field, field_name, comb_info, value_to_set or "", "cloned")

                # Process child fields
                if '/Kids' in field:
                    self._apply_values_recursive(field['/Kids'], field_values, full_name, field_comb_data)

            except Exception as e:
                print(f"Error applying field value: {e}")
                continue

    def fill_pdf_from_json(self, json_path: str, output_path: str) -> bool:
        """Main method to fill PDF from JSON data"""
        print(f"Loading field data from: {json_path}")
        json_data = self.load_field_data(json_path)

        if not json_data:
            print("Failed to load JSON data")
            return False

        print(f"Opening PDF file: {self.pdf_path}")
        if not self.open_pdf():
            print("Failed to open PDF file")
            return False

        # Check PDF permissions
        print("Checking PDF permissions...")
        permissions = self.check_pdf_permissions()
        if not permissions['can_fill_forms']:
            print("WARNING: PDF may not allow form filling")
        if not permissions['can_modify']:
            print("WARNING: PDF may not allow modifications")

        try:
            # Extract field values and comb field data from JSON
            field_values, field_comb_data = self.extract_field_values(json_data)

            if not field_values:
                print("No field values found in JSON data")
                return False

            print(f"Found {len(field_values)} field values to fill")
            if field_comb_data:
                print(f"Found {len(field_comb_data)} comb fields with spacing data")

            # Fill the form fields with values and comb data
            if not self.fill_form_fields(field_values, field_comb_data):
                print("Failed to fill form fields")
                return False

            # Save the filled PDF
            if not self.save_pdf(output_path):
                print("Failed to save filled PDF")
                return False

            return True

        finally:
            self.close_pdf()


def main():
    """Main function"""
    # Default file paths
    pdf_path = "fw9.pdf"
    json_path = "numberOfCells_fields.json"
    output_path = "out.pdf"

    # Allow command line arguments
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    if len(sys.argv) > 2:
        json_path = sys.argv[2]
    if len(sys.argv) > 3:
        output_path = sys.argv[3]

    print(f"PDF Form Filling Tool (PyPDF v5.6)")
    print(f"Source PDF: {pdf_path}")
    print(f"Field data JSON: {json_path}")
    print(f"Output PDF: {output_path}")
    print("-" * 50)

    # Create PDF filler and process
    filler = PDFFormFiller(pdf_path)
    success = filler.fill_pdf_from_json(json_path, output_path)

    if success:
        print("\n✓ PDF form filling completed successfully!")
    else:
        print("\n✗ PDF form filling failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()