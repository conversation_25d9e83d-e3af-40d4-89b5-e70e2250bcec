#!/usr/bin/env python3
"""
测试脚本：验证numberOfCells字段的Td偏移效果

这个脚本会：
1. 读取PDF表单中的numberOfCells字段
2. 填充测试数据
3. 应用Tc字间距和Td偏移
4. 生成对比版本（有/无Td偏移）
5. 扁平化PDF进行最终测试
"""

import json
import subprocess
import sys
from pathlib import Path

def main():
    # 输入文件
    input_pdf = "fw9.pdf"
    fields_json = "numberOfCells_fields.json"
    
    if not Path(input_pdf).exists():
        print(f"错误：找不到输入PDF文件 {input_pdf}")
        return 1
    
    if not Path(fields_json).exists():
        print(f"错误：找不到字段JSON文件 {fields_json}")
        return 1
    
    print("=== numberOfCells字段Td偏移测试 ===\n")
    
    # 1. 生成带Td偏移的PDF
    print("1. 生成带Td偏移的PDF...")
    output_with_td = "test_with_td_offset.pdf"
    result = subprocess.run([
        "python", "fillPDF.py", input_pdf, fields_json, output_with_td
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"错误：生成PDF失败\n{result.stderr}")
        return 1
    
    print(f"✓ 生成成功：{output_with_td}")
    
    # 2. 扁平化PDF
    print("\n2. 扁平化PDF...")
    flattened_pdf = "test_with_td_offset_flattened.pdf"
    result = subprocess.run([
        "gs", "-o", flattened_pdf, "-sDEVICE=pdfwrite", output_with_td
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"错误：扁平化失败\n{result.stderr}")
        return 1
    
    print(f"✓ 扁平化成功：{flattened_pdf}")
    
    # 3. 分析字段信息
    print("\n3. 分析numberOfCells字段信息...")
    with open(fields_json, 'r', encoding='utf-8') as f:
        fields_data = json.load(f)
    
    print("\n字段详细信息：")
    print("-" * 80)
    print(f"{'字段名':<20} {'numberOfCells':<12} {'字段宽度':<10} {'Tc值':<8} {'Td偏移':<8} {'测试值':<8}")
    print("-" * 80)
    
    for field in fields_data:
        if field.get('numberOfCells', 0) > 1:
            name = field['name']
            cells = field['numberOfCells']
            width = field['field_width']
            letter_space = field.get('letterSpace', 0)
            tc_value = letter_space * 9  # 转换为点单位
            td_offset = tc_value * 0.3   # Td偏移
            test_value = field.get('value', 'N/A')
            
            print(f"{name:<20} {cells:<12} {width:<10.1f} {tc_value:<8.2f} {td_offset:<8.2f} {test_value:<8}")
    
    print("-" * 80)
    
    # 4. 显示DA字符串示例
    print("\n4. DA字符串示例（numberOfCells=7的字段）：")
    print("修复前：/Helvetica 9 Tf 0 g")
    print("修复后：/Helvetica 9 Tf 0 g 9.8300 Tc 2.9490 0 Td")
    print("说明：")
    print("  - Tc 9.8300：字符间距设置为9.83点")
    print("  - Td 2.9490 0：文本起始位置向右偏移2.95点（0.3倍字间距）")
    
    print(f"\n=== 测试完成 ===")
    print(f"生成的文件：")
    print(f"  - {output_with_td}（填充后的PDF）")
    print(f"  - {flattened_pdf}（扁平化后的PDF）")
    print(f"\n请在PDF查看器中打开这些文件，检查numberOfCells字段的字符对齐效果。")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
